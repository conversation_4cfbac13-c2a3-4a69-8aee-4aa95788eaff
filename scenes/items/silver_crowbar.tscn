[gd_scene load_steps=6 format=3 uid="uid://ci773u35iek6y"]

[ext_resource type="Texture2D" uid="uid://j6edle4gs6tp" path="res://images/items/KTCRH0.png" id="1_srs15"]
[ext_resource type="Script" uid="uid://ck4p816vkcs66" path="res://scenes/items/wieldable.gd" id="1_wihrt"]
[ext_resource type="Resource" uid="uid://djpa0nptavvfd" path="res://weapon_manager/weapons/silver_crowbar.tres" id="2_yu2la"]
[ext_resource type="AudioStream" uid="uid://lkgpafvrsw8t" path="res://sounds/sfx/CROWPICK.wav" id="3_yu2la"]

[sub_resource type="BoxShape3D" id="BoxShape3D_srs15"]
size = Vector3(0.230883, 0.664707, 0.663136)

[node name="SilverCrowbar" type="Area3D"]
script = ExtResource("1_wihrt")
wieldable_item = ExtResource("2_yu2la")
pickup_sound = ExtResource("3_yu2la")

[node name="Sprite3D" type="Sprite3D" parent="."]
transform = Transform3D(0.3, 0, 0, 0, 0.3, 0, 0, 0, 0.3, 0, 0, 0)
billboard = 2
shaded = true
texture = ExtResource("1_srs15")

[node name="CollisionShape3D" type="CollisionShape3D" parent="."]
transform = Transform3D(-4.37114e-08, -1, 0, 1, -4.37114e-08, 0, 0, 0, 1, -0.0330665, 0.00377772, -0.00449944)
shape = SubResource("BoxShape3D_srs15")

[connection signal="body_entered" from="." to="." method="_on_body_entered"]
